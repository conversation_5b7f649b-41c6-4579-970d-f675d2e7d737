---
title: Bootstrap Template Guide
description: Using Bootstrap Templates Downloaded from GrayGrids
date: 2023-1-25
---

## Introduction

This documentation covers the instruction on how to install and use the Bootstrap templates, and answers to any common problems and issues you may face while using the templates.

If you have any question that is beyond the scope of this documentation feel free to [Open a Support Ticket](https://uideck.com/support/) or reach out to us on [Discord](https://pimjo.com/discord).

We will get back to you ASAP.

## Downloading Templates:

You can download all available templates that are available under the big bundle, by visiting your account.

You can download it anytime you want, as long as you have an account with us.

## **Installing Templates**:

To be able to use the template, first, you’ll have to install it. Here are the steps you will have to take to install it:

**Step#1:** Download the template from GrayGrids. When you download the template, you’ll get a zip file.

**Step#2**: Unzip/Extract the zip file. After you’ve extracted it you’ll find all the files and assets you’ll need.

**Step#3**: Now open up the folder in your code editor and update the content according to your need.

**Step#4**: After you’ve updated the content, now go to your cPanel or open your FTP client (like Filezilla) and upload the template on your server. Make sure to upload it to the root directory.

Installing is done, now go to **yourdomainname.com/index.html**. You should see the website live.

## **Directory Structure**

When you extract the template folder, you’ll see something like this inside the folder:

```
|-- Template Folder
    |-- assets
    |    |-- css
    |       |-- All CSS Files
    |    |-- scss
    |       |-- All SCSS Files
    |    |-- js
    |       |-- All JS Files
    |    |-- fonts
    |       |-- All Fonts Files
    |    |-- images
    |       |-- All Images
    |-- index.html and All HTML Files
```

## **Customization**

### **Changing Default Theme Color**

If you want to change the default Theme Color, open `assets->css>main.css` file. And find the default color and replace it with your preferred color.

**Note:** If you are familiar with Scss, you can change the color by changing the Scss variable.

### **Changing Default Font Family**

Just like the color if you want to change the default Font, then go to `assets->css>main.css`

and find all the font-family then replace its property with your own font-family.

Make sure to include the font’s URL in your HTML or CSS fle. Otherwise, it won’t work.

**Note:** If you are familiar with Scss, you change the font-family by changing the Scss variable.

## **Plugins**

To add interaction to the templates we’ve used these Vanilla JS and CSS Plugins.

- [Glightbox](https://biati-digital.github.io/glightbox/)
- [Tiny Slider](https://github.com/ganlanyuan/tiny-slider)
- [Wow JS](https://wowjs.uk/docs.html)
- [Animate CSS](https://animate.style/) - with some modification

## **Images**

We used images from Unsplash

- [Unsplash](https://unsplash.com/)

## **Icons**

For icons, we’ve used Lineicons.

- [Lineicons](https://lineicons.com/)
